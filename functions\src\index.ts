// Complete functions index with Stripe API
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import { createHash } from 'crypto';
import cors from 'cors';
import Stripe from 'stripe';

// Initialize Firebase Admin
admin.initializeApp();

// Initialize Stripe
const stripe = new Stripe(functions.config().stripe?.api_key || process.env.STRIPE_API_KEY || '', {
  apiVersion: '2025-06-30.basil',
});

// Types for listings
type ListingCondition = 'new' | 'like_new' | 'very_good' | 'good' | 'fair' | 'poor';
type ListingType = 'sell' | 'rent' | 'auction';
type ListingStatus = 'active' | 'sold' | 'inactive' | 'pending';

// Helper functions
const verifyAuth = async (context: functions.https.CallableContext): Promise<admin.auth.DecodedIdToken> => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }
  return context.auth as unknown as admin.auth.DecodedIdToken;
};

const handleError = (error: unknown): never => {
  console.error('Function error:', error);

  if (error instanceof functions.https.HttpsError) {
    throw error;
  }

  const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
  throw new functions.https.HttpsError(
    'internal',
    errorMessage,
    error
  );
};

console.log('🚀 Minimal Firebase Functions with Listings loading...');

// Create a new listing
export const createListing = functions.https.onCall(async (data, context) => {
  try {
    console.log('Creating listing with data:', JSON.stringify(data, null, 2));
    const auth = await verifyAuth(context);

    const {
      title,
      description,
      price,
      category,
      condition,
      type,
      imageURLs
    } = data;
    
    // Validate required fields
    if (!title || !description || price === undefined || !category || !condition || !type) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Missing required fields'
      );
    }
    
    // Validate listing type
    if (!['sell', 'rent', 'auction'].includes(type)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid listing type. Must be one of: sell, rent, auction'
      );
    }
    
    // Validate condition
    if (!['new', 'like_new', 'very_good', 'good', 'fair', 'poor'].includes(condition)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid condition. Must be one of: new, like_new, very_good, good, fair, poor'
      );
    }
    
    // Get user data to include university
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'User not found'
      );
    }

    const userData = userDoc.data();
    console.log('User data:', JSON.stringify(userData, null, 2));

    // Get university from user data or extract from email
    let university = userData?.university;
    console.log('Initial university:', university);

    if (!university && userData?.email) {
      console.log('Extracting university from email:', userData.email);
      // Extract university from email domain as fallback
      const emailParts = userData.email.split('@');
      const domain = emailParts[1];
      university = domain.split('.')[0];
      // Capitalize university name
      university = university.charAt(0).toUpperCase() + university.slice(1);
      console.log('Extracted university:', university);

      // Update user profile with university
      await admin.firestore().collection('users').doc(auth.uid).update({
        university,
        updatedAt: admin.firestore.Timestamp.now()
      });
    } else if (!university && auth.token?.email) {
      console.log('Extracting university from auth token email:', auth.token.email);
      // Try to get email from auth token as fallback
      const emailParts = auth.token.email.split('@');
      const domain = emailParts[1];
      university = domain.split('.')[0];
      // Capitalize university name
      university = university.charAt(0).toUpperCase() + university.slice(1);
      console.log('Extracted university from token:', university);

      // Update user profile with university and email
      await admin.firestore().collection('users').doc(auth.uid).update({
        university,
        email: auth.token.email,
        updatedAt: admin.firestore.Timestamp.now()
      });
    }

    if (!university) {
      console.error('Unable to determine university. User data:', userData, 'Auth token:', auth.token);
      throw new functions.https.HttpsError(
        'failed-precondition',
        'Unable to determine university from user profile or email. Please update your profile.'
      );
    }

    console.log('Final university:', university);

    // Create the listing object
    const listing: any = {
      title,
      description,
      price: Number(price),
      category,
      condition: condition as ListingCondition,
      type: type as ListingType,
      ownerId: auth.uid,
      ownerName: userData?.name || 'Anonymous',
      university: university,
      imageURLs: imageURLs || [],
      status: 'active' as ListingStatus,
      visibility: data.visibility || 'university',
      createdAt: admin.firestore.Timestamp.now(),

      // Add delivery method fields
      deliveryMethod: data.deliveryMethod || 'in_person'
    };

    // Only add shippingOptions if it exists and has valid data
    if (data.shippingOptions && Object.keys(data.shippingOptions).length > 0) {
      listing.shippingOptions = data.shippingOptions;
    }

    // Add type-specific fields
    if (type === 'rent') {
      if (data.rentalPeriod) listing.rentalPeriod = data.rentalPeriod;
      if (data.weeklyPrice) listing.weeklyPrice = Number(data.weeklyPrice);
      if (data.monthlyPrice) listing.monthlyPrice = Number(data.monthlyPrice);
      if (data.startDate) listing.startDate = data.startDate;
      if (data.endDate) listing.endDate = data.endDate;
    }

    if (type === 'auction') {
      if (data.startingBid) listing.startingBid = Number(data.startingBid);
      if (data.auctionStartDate) listing.auctionStartDate = data.auctionStartDate;
      if (data.auctionStartTime) listing.auctionStartTime = data.auctionStartTime;
      if (data.auctionEndDate) listing.auctionEndDate = data.auctionEndDate;
      if (data.auctionEndTime) listing.auctionEndTime = data.auctionEndTime;
      if (data.auctionDuration) listing.auctionDuration = data.auctionDuration;
    }
    
    // Add to Firestore
    console.log('Adding listing to Firestore:', JSON.stringify(listing, null, 2));
    const docRef = await admin.firestore().collection('listings').add(listing);
    console.log('Listing created successfully with ID:', docRef.id);

    return {
      success: true,
      data: {
        id: docRef.id,
        ...listing
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Edit an existing listing
export const editListing = functions.https.onCall(async (data, context) => {
  try {
    console.log('Editing listing with data:', JSON.stringify(data, null, 2));
    const auth = await verifyAuth(context);

    const {
      listingId,
      title,
      description,
      price,
      category,
      condition,
      type,
      imageURLs,
      status
    } = data;

    if (!listingId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Listing ID is required'
      );
    }

    // Get the listing
    const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();

    if (!listingDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'Listing not found'
      );
    }

    const listing = listingDoc.data();

    // Check if the user is the owner
    if (listing?.ownerId !== auth.uid) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only the owner can edit this listing'
      );
    }

    // Create update object
    const updateData: any = {
      updatedAt: admin.firestore.Timestamp.now()
    };

    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (price !== undefined) updateData.price = Number(price);
    if (category !== undefined) updateData.category = category;
    if (condition !== undefined) updateData.condition = condition;
    if (type !== undefined) {
      if (!['sell', 'rent', 'auction'].includes(type)) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Invalid listing type. Must be one of: sell, rent, auction'
        );
      }
      updateData.type = type;
    }
    if (imageURLs !== undefined) updateData.imageURLs = imageURLs;
    if (status !== undefined) {
      if (!['active', 'sold', 'pending', 'deleted'].includes(status)) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Invalid status. Must be one of: active, sold, pending, deleted'
        );
      }
      updateData.status = status;
    }

    // Update the listing
    await admin.firestore().collection('listings').doc(listingId).update(updateData);

    console.log('Listing updated successfully:', listingId);

    return {
      success: true,
      data: {
        id: listingId,
        ...listing,
        ...updateData
      }
    };
  } catch (error) {
    console.error('Error editing listing:', error);
    return handleError(error);
  }
});

// Test function to verify deployment
export const testListings = functions
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Listings functions working',
      timestamp: new Date().toISOString()
    });
  });

// Stripe API for checkout sessions
export const stripeApi = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60,
  })
  .https.onRequest(async (req, res) => {
    // Enable CORS for all origins
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.set('Access-Control-Allow-Credentials', 'true');

    if (req.method === 'OPTIONS') {
      res.status(200).send();
      return;
    }

    try {
      const path = req.path;
      console.log(`🔗 StripeApi request: ${req.method} ${path}`);

      if (path === '/create-checkout-session' && req.method === 'POST') {
        // Handle checkout session creation
        try {
          // Check authorization
          const authHeader = req.headers.authorization;
          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({ error: 'Unauthorized - Missing or invalid authorization header' });
            return;
          }

          const token = authHeader.split('Bearer ')[1];
          const decodedToken = await admin.auth().verifyIdToken(token);
          const buyerId = decodedToken.uid;

          // Get request data
          const { listingId, useWalletBalance = false, orderDetails } = req.body;

          if (!listingId) {
            res.status(400).json({ error: 'Listing ID is required' });
            return;
          }

          console.log(`🛒 Creating checkout for listing: ${listingId}, buyer: ${buyerId}`);
          console.log(`🔍 Request data:`, JSON.stringify({ listingId, useWalletBalance, orderDetails }, null, 2));

          // Get listing details
          const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
          if (!listingDoc.exists) {
            res.status(404).json({ error: 'Listing not found' });
            return;
          }

          const listing = listingDoc.data();
          if (!listing) {
            res.status(404).json({ error: 'Listing data not found' });
            return;
          }

          // Calculate amounts
          const itemPrice = listing.price;
          const shippingCost = orderDetails?.shippingFee || 0;
          const walletBalanceUsed = orderDetails?.appliedWalletCredit || 0;
          const totalBeforeWallet = itemPrice + shippingCost;
          const finalAmount = Math.max(0, totalBeforeWallet - walletBalanceUsed);

          console.log(`💰 Pricing: Item: $${itemPrice}, Shipping: $${shippingCost}, Wallet: $${walletBalanceUsed}, Final: $${finalAmount}`);

          // Create order document
          const orderId = admin.firestore().collection('orders').doc().id;
          const secretCode = Math.floor(100000 + Math.random() * 900000).toString();

          const orderData = {
            id: orderId,
            listingId,
            buyerId,
            sellerId: listing.ownerId,
            status: finalAmount <= 0 ? 'payment_succeeded' : 'pending_payment',
            totalAmount: finalAmount,
            itemPrice,
            shippingCost,
            walletBalanceUsed,
            originalTotal: totalBeforeWallet,
            secretCode: finalAmount <= 0 ? secretCode : null,
            paymentCompletedAt: finalAmount <= 0 ? admin.firestore.Timestamp.now() : null,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now(),
            deliveryMethod: orderDetails?.deliveryMethod || listing.deliveryMethod || 'in_person',
            shippingAddress: orderDetails?.shippingAddress || null
          };

          await admin.firestore().collection('orders').doc(orderId).set(orderData);

          // If final amount is 0 (fully paid with wallet), handle wallet-only payment
          if (finalAmount <= 0) {
            console.log(`🎉 Order fully covered by wallet balance!`);

            // Deduct from wallet balance
            if (walletBalanceUsed > 0) {
              const walletRef = admin.firestore().collection('wallets').doc(buyerId);
              await admin.firestore().runTransaction(async (transaction) => {
                const walletDoc = await transaction.get(walletRef);
                const currentBalance = walletDoc.exists ? (walletDoc.data()?.balance || 0) : 0;

                if (currentBalance < walletBalanceUsed) {
                  throw new Error('Insufficient wallet balance');
                }

                const newBalance = currentBalance - walletBalanceUsed;
                const walletTransaction = {
                  id: admin.firestore().collection('temp').doc().id,
                  type: 'debit',
                  amount: walletBalanceUsed,
                  description: `Purchase: ${listing.title}`,
                  orderId,
                  timestamp: admin.firestore.Timestamp.now(),
                  balanceAfter: newBalance
                };

                transaction.update(walletRef, {
                  balance: newBalance,
                  history: admin.firestore.FieldValue.arrayUnion(walletTransaction),
                  lastUpdated: admin.firestore.Timestamp.now()
                });
              });
            }

            // Mark listing as sold
            await admin.firestore().collection('listings').doc(listingId).update({
              status: 'sold',
              soldAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });

            res.status(200).json({
              success: true,
              paidWithWallet: true,
              walletAmountUsed: walletBalanceUsed,
              orderId,
              secretCode,
              message: `Order paid successfully using $${walletBalanceUsed} from wallet balance`
            });
            return;
          }

          // Create Stripe checkout session for remaining amount
          const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [{
              price_data: {
                currency: 'usd',
                product_data: {
                  name: `${listing.title}${walletBalanceUsed > 0 ? ` (after $${walletBalanceUsed} wallet credit)` : ''}`,
                  description: listing.description || 'Hive Campus item',
                },
                unit_amount: Math.round(finalAmount * 100), // Convert to cents
              },
              quantity: 1,
            }],
            mode: 'payment',
            success_url: `https://h1c1-798a8.web.app/order-success?session_id={CHECKOUT_SESSION_ID}&order_id=${orderId}`,
            cancel_url: `https://h1c1-798a8.web.app/listing/${listingId}`,
            metadata: {
              orderId,
              listingId,
              buyerId,
              sellerId: listing.ownerId,
              walletAmountUsed: walletBalanceUsed.toString(),
              originalTotal: totalBeforeWallet.toString(),
              finalAmount: finalAmount.toString(),
            },
          });

          // Update order with session ID
          await admin.firestore().collection('orders').doc(orderId).update({
            stripeSessionId: session.id,
            updatedAt: admin.firestore.Timestamp.now()
          });

          console.log(`✅ Checkout session created: ${session.id}`);

          res.status(200).json({
            success: true,
            sessionId: session.id,
            sessionUrl: session.url,
            orderId,
            finalAmount
          });

        } catch (error) {
          console.error('❌ Error creating checkout session:', error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          res.status(500).json({ error: errorMessage });
        }
      } else {
        res.status(404).json({ error: 'Endpoint not found' });
      }
    } catch (error) {
      console.error('Error in stripeApi:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });

// Get listings with filtering
export const getListings = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);

    const {
      university,
      category,
      type,
      condition,
      minPrice,
      maxPrice,
      ownerId,
      status = 'active',
      limit = 20,
      lastVisible
    } = data;

    // Get current user's university for visibility filtering
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
    const currentUserUniversity = userDoc.data()?.university;

    let query = admin.firestore().collection('listings')
      .where('status', '==', status)
      .orderBy('createdAt', 'desc');

    // Apply filters if provided
    if (university) {
      query = query.where('university', '==', university);
    }

    if (category) {
      query = query.where('category', '==', category);
    }

    if (type) {
      query = query.where('type', '==', type);
    }

    if (condition) {
      query = query.where('condition', '==', condition);
    }

    if (ownerId) {
      query = query.where('ownerId', '==', ownerId);
    }

    // Apply pagination
    if (lastVisible) {
      const lastDoc = await admin.firestore().collection('listings').doc(lastVisible).get();
      if (lastDoc.exists) {
        query = query.startAfter(lastDoc);
      }
    }

    query = query.limit(limit);

    const snapshot = await query.get();
    const listings: any[] = [];
    let lastVisibleId = null;

    snapshot.forEach(doc => {
      const listing = doc.data();

      // Apply visibility filtering
      const isVisible = listing.visibility === 'public' ||
                       (listing.visibility === 'university' && listing.university === currentUserUniversity);

      if (!isVisible) {
        return; // Skip this listing
      }

      // Apply price filtering in memory
      const price = listing.price;
      if ((minPrice === undefined || price >= minPrice) &&
          (maxPrice === undefined || price <= maxPrice)) {
        listings.push({
          id: doc.id,
          ...listing
        });
      }

      lastVisibleId = doc.id;
    });

    return {
      success: true,
      data: {
        listings,
        lastVisible: lastVisibleId,
        total: listings.length
      }
    };
  } catch (error) {
    console.error('Error getting listings:', error);
    return handleError(error);
  }
});

// Get a single listing by ID
export const getListingById = functions.https.onCall(async (data, context) => {
  try {
    const auth = await verifyAuth(context);

    const { listingId } = data;

    if (!listingId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Listing ID is required'
      );
    }

    const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();

    if (!listingDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'Listing not found'
      );
    }

    const listing = listingDoc.data();

    // Don't return deleted listings unless it's the owner
    if (listing?.status === 'deleted' && listing.ownerId !== auth.uid) {
      throw new functions.https.HttpsError(
        'not-found',
        'Listing not found'
      );
    }

    return {
      success: true,
      data: {
        id: listingDoc.id,
        ...listing
      }
    };
  } catch (error) {
    console.error('Error getting listing by ID:', error);
    return handleError(error);
  }
});

// Get Stripe Connect account status
export const getStripeConnectAccountStatus = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      const auth = await verifyAuth(context);
      const userId = auth.uid;

      console.log(`Getting Stripe Connect status for user: ${userId}`);

      // Get connect account from Firestore
      const connectAccountDoc = await admin.firestore()
        .collection('connectAccounts')
        .doc(userId)
        .get();

      if (!connectAccountDoc.exists) {
        console.log('No connect account found for user');
        return {
          accountId: null,
          onboardingUrl: null,
          dashboardUrl: null,
          isOnboarded: false,
          chargesEnabled: false,
          payoutsEnabled: false,
        };
      }

      const connectAccount = connectAccountDoc.data();
      console.log('Connect account data:', connectAccount);

      return {
        accountId: connectAccount?.stripeAccountId || null,
        onboardingUrl: connectAccount?.onboardingUrl || null,
        dashboardUrl: connectAccount?.dashboardUrl || null,
        isOnboarded: connectAccount?.isOnboarded || false,
        chargesEnabled: connectAccount?.chargesEnabled || false,
        payoutsEnabled: connectAccount?.payoutsEnabled || false,
      };
    } catch (error) {
      console.error('Error in getStripeConnectAccountStatus:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get seller pending payouts
export const getSellerPendingPayouts = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      const auth = await verifyAuth(context);
      const sellerId = auth.uid;

      console.log(`Getting pending payouts for seller: ${sellerId}`);

      // Get orders where this user is the seller and has pending payouts
      const ordersSnapshot = await admin.firestore()
        .collection('orders')
        .where('sellerId', '==', sellerId)
        .where('status', 'in', ['payment_succeeded', 'delivered'])
        .get();

      const pendingPayouts: any[] = [];
      let totalPendingAmount = 0;

      ordersSnapshot.forEach(doc => {
        const order = doc.data();

        // Check if order has pending payout
        if (order.pendingPayout && order.pendingPayout > 0) {
          pendingPayouts.push({
            orderId: doc.id,
            ...order,
            pendingAmount: order.pendingPayout
          });
          totalPendingAmount += order.pendingPayout;
        }
      });

      return {
        success: true,
        data: {
          pendingPayouts,
          totalPendingAmount,
          count: pendingPayouts.length
        }
      };
    } catch (error) {
      console.error('Error getting seller pending payouts:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });
